export interface Group {
	workGroup: {
		id: number;
		name: string;
		createUser: string;
		business: string;
	};
	manager: string; // 后端保存格式：字符串，如 "user1;user2"
	member: string;
	admin: string;
	managerArray: string[]; // 用于前端编辑界面绑定
	memberArray: string[];
	adminArray: string[];
	createAt: string;
	updateAt: string;
}

export const getInitialFormData = (): Group => ({
	id: 0,
	name: '',
	createUser: '',
	business: '',
	managerArray: [],
	memberArray: [],
	adminArray: [],
	createAt: '',
	updateAt: '',
});
