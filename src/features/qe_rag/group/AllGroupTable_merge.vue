<template>
	<el-table :data="tableData" v-loading="loading" style="width: 100%">
		<el-table-column property="name" label="名称" show-overflow-tooltip />
		<el-table-column property="admin" label="管理员" width="200" show-overflow-tooltip>
			<template #default="scope">
				<div class="member-tags">
					<el-tag
						v-for="(member, index) in splitMembers(scope.row.admin)"
						:key="index"
						class="member-tag"
						size="small"
					>
						{{ member }}
					</el-tag>
				</div>
			</template>
		</el-table-column>
		<el-table-column property="member" label="成员" width="200" show-overflow-tooltip>
			<template #default="scope">
				<div class="member-tags">
					<el-tag
						v-for="(member, index) in splitMembers(scope.row.member)"
						:key="index"
						class="member-tag"
						size="small"
					>
						{{ member }}
					</el-tag>
				</div>
			</template>
		</el-table-column>
		<el-table-column property="createUser" label="创建者" show-overflow-tooltip></el-table-column>
		<el-table-column property="business" label="团队" show-overflow-tooltip></el-table-column>
		<el-table-column property="manager" label="经理" show-overflow-tooltip></el-table-column>
		<el-table-column property="createAt" label="创建时间" show-overflow-tooltip>
			<template #default="scope">
				{{ formatDate(scope.row.createAt) }}
			</template>
		</el-table-column>
		<el-table-column property="updateAt" label="更新时间" show-overflow-tooltip>
			<template #default="scope">
				{{ formatDate(scope.row.updateAt) }}
			</template>
		</el-table-column>

		<el-table-column label="操作" width="100" fixed="right">
			<template #default="scope">
				<el-button
					size="small"
					type="primary"
					@click="handleEdit(scope.row)"
					icon="Edit"
					:disabled="!canEdit(scope.row)"
				>
					编辑
				</el-button>
				<!-- <el-button size="small" type="danger" @click="handleDelete(scope.row)" icon="Delete">删除</el-button> -->
			</template>
		</el-table-column>
	</el-table>

	<!-- 编辑对话框 -->
	<el-dialog v-model="dialogVisible" :title="dialogTitle" width="50%">
		<el-form :model="formData" label-width="100px">
			<el-form-item
				label="名称"
				prop="name"
				:rules="[{ required: true, message: '请输入名称', trigger: 'blur' }]"
			>
				<el-input v-model="formData.name" />
			</el-form-item>
			<el-form-item label="创建者">
				<el-tag>{{ formData.createUser }}</el-tag>
			</el-form-item>
			<el-form-item
				label="团队"
				prop="business"
				:rules="[{ required: true, message: '请输入团队名称', trigger: 'blur' }]"
			>
				<el-input v-model="formData.business" />
			</el-form-item>
			<el-form-item label="经理">
				<UserSelect
					style="width: 100%"
					v-model="formData.managerArray"
					:default-selected="formData.managerArray"
					@change="handleManagerChange"
				/>
			</el-form-item>
			<el-form-item label="成员">
				<UserSelect
					style="width: 100%"
					v-model="formData.memberArray"
					:default-selected="formData.memberArray"
					@change="handleOwnersChange"
				/>
			</el-form-item>
			<el-form-item label="管理员">
				<UserSelect
					style="width: 100%"
					v-model="formData.adminsArray"
					:default-selected="formData.adminsArray"
					@change="handleAdminsChange"
				/>
			</el-form-item>
		</el-form>

		<template #footer>
			<el-button @click="dialogVisible = false">取消</el-button>
			<el-button type="primary" @click="submitForm" :loading="formLoading">确认</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import { useUserStore } from '@/stores/user';
import GroupApi from '@/api/GroupApi.ts';
import type { Group } from '@/types/group';
import UserSelect from '@/views/knowledge/Books/components/UserSelect.vue';

const userStore = useUserStore();
export default defineComponent({
	name: 'ALLGroupTable',
	components: { UserSelect },
	data() {
		return {
			tableData: [] as Group[],
			loading: false,
			dialogVisible: false,
			dialogTitle: '编辑群组',
			formData: {
				id: 0,
				name: '',
				createUser: '',
				business: '',
				manager: '',
				member: '',
				memberArray: [] as string[], // 用于多选组件
				managerArray: [] as string[],
				adminsArray: [] as string[],
				createAt: '',
				updateAt: '',
			} as any,
			isEditMode: false,
			formLoading: false,
			allMembers: ['nieyuxin', 'lirui42', 'guanlin', 'mazhe', 'yuanli06'], // 假设的成员列表
		};
	},
	created() {
		this.getTableData();
	},
	methods: {
		async getTableData() {
			try {
				this.loading = true;
				const res = await GroupApi.getGroups();
				this.tableData = res.data;
			} catch (error) {
				ElMessage.error('获取群组数据失败');
			} finally {
				this.loading = false;
			}
		},
		handleManagerChange(users: string[]) {
			this.formData.managerArray = users;
		},
		handleAdminsChange(users: string[]) {
			this.formData.adminsArray = users;
		},
		handleOwnerChange(users: string[]) {
			this.formData.memberArray = users;
		},
		canEdit(row: Group): boolean {
			const { username } = userStore; // 假设用户信息中有username字段
			if (!username) return false;
			console.log('当前用户:', username);
			// 检查当前用户是否在admin列表中
			return this.splitMembers(row.admin).includes(username);
		},

		// 其他方法保持不变...
		handleEdit(row: Group) {
			if (!this.canEdit(row)) {
				ElMessage.warning('您没有权限编辑此群组');
				return;
			}

			this.dialogTitle = '编辑群组';
			this.isEditMode = true;
			this.formData = {
				...row,
				adminsArray: this.splitMembers(row.admin),
				managerArray: this.splitMembers(row.manager),
				memberArray: this.splitMembers(row.member),
			};
			this.dialogVisible = true;
		},
		splitMembers(memberString: string) {
			return memberString?.split(';').filter(Boolean) || [];
		},

		formatDate(dateString: string) {
			return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
		},

		async submitForm() {
			try {
				this.formLoading = true;
				// 将成员数组转换回字符串格式
				const submitData = {
					...this.formData,
					member: this.formData.memberArray.join(';'),
				};

				if (this.isEditMode) {
					await GroupApi.updateGroup(submitData);
					ElMessage.success('更新成功');
				} else {
					await GroupApi.createGroup(submitData);
					ElMessage.success('创建成功');
				}
				this.dialogVisible = false;
				await this.getTableData();
			} catch (error) {
				console.error('操作失败:', error);
				ElMessage.error('操作失败');
			} finally {
				this.formLoading = false;
			}
		},

		handleDelete(row: Group) {
			ElMessageBox.confirm(`确定要删除群组 "${row.name}" 吗?`, '警告', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(async () => {
					try {
						this.loading = true;
						await GroupApi.deleteGroup(row.id);
						ElMessage.success('删除成功');
						await this.getTableData();
					} catch (error) {
						console.error('删除失败:', error);
						ElMessage.error('删除失败');
					} finally {
						this.loading = false;
					}
				})
				.catch(() => {
					// 用户取消操作
				});
		},
	},
});
</script>

<style scoped>
.member-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 4px;
}
.member-tag {
	margin: 2px;
}
.el-button + .el-button {
	margin-left: 8px;
}
</style>
