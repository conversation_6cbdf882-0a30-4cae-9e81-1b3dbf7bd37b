<template>
	<el-tabs v-model="activeName" class="group-tabs">
		<el-tab-pane name="1">
			<template #label>
				<div class="tab-label">
					<el-icon class="tab-icon"><User /></el-icon>
					<span class="tab-text">我加入的</span>
				</div>
			</template>
			<!-- 只有当 activeName 为 '1' 时，才渲染组件 -->
			<group-table v-if="activeName === '1'" />
		</el-tab-pane>

		<el-tab-pane name="2">
			<template #label>
				<div class="tab-label">
					<el-icon class="tab-icon"><Coin /></el-icon>
					<span class="tab-text">全部团队</span>
				</div>
			</template>
			<!-- 只有当 activeName 为 '2' 时，才渲染组件 -->
			<all-group-table v-if="activeName === '2'" />
		</el-tab-pane>

		<el-tab-pane name="3">
			<template #label>
				<div class="tab-label">
					<el-icon class="tab-icon"><Coin /></el-icon>
					<span class="tab-text">待审批列表</span>
				</div>
			</template>
			<!-- 只有当 activeName 为 '3' 时，才渲染组件 -->
			<table-list v-if="activeName === '3'" />
		</el-tab-pane>
	</el-tabs>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import AllGroupTable from './AllGroupTable.vue';
import groupTable from './groupTable.vue';
import tableList from './tableList.vue';

const route = useRoute();
const router = useRouter();
const activeName = ref('1'); // 默认值

// 初始化时从路由参数获取id
onMounted(() => {
	if (route.query.id) {
		activeName.value = route.query.id.toString();
		console.log('初始化activeName:', activeName.value);
	}
});

// 监听activeName变化
watch(activeName, (newVal) => {
	console.log('标签页变化:', newVal);
	router.replace({
		path: route.path,
		query: { ...route.query, id: newVal },
	});
});

// 监听路由参数变化
watch(
	() => route.query.id,
	(newId) => {
		if (newId && newId !== activeName.value) {
			console.log('路由参数变化:', newId);
			activeName.value = newId.toString();
		}
	},
	{ immediate: true }
);
</script>
<style lang="less" scoped>
.group-tabs {
	margin: 5px;
	padding: 10px;
	background-color: #fff;
	height: 100%;

	:deep(.el-tabs__header) {
		margin-bottom: 0;
	}

	:deep(.el-tabs__nav-wrap) {
		&::after {
			height: 1px;
			background-color: var(--el-border-color-light);
		}
	}

	:deep(.el-tabs__item) {
		padding: 0 20px;
		height: 48px;

		&:hover {
			color: var(--el-color-primary);
		}

		&.is-active {
			color: var(--el-color-primary);
		}
	}

	.tab-label {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 6px;

		.tab-icon {
			font-size: 16px;
			margin-right: 4px;
		}

		.tab-text {
			font-size: 14px;
			line-height: 1;
		}
	}
}
</style>
