<template>
	<el-table :data="tableData" v-loading="loading" style="width: 100%">
    <el-table-column property="id" label="工作组ID" show-overflow-tooltip />
    <el-table-column property="name" label="工作组名称" show-overflow-tooltip />
		<el-table-column property="admin" label="管理员" width="200" show-overflow-tooltip>
			<template #default="scope">
				<div class="member-tags">
					<el-tag
						v-for="(member, index) in splitMembers(scope.row.admin)"
						:key="index"
						class="member-tag"
						size="small"
					>
						{{ member }}
					</el-tag>
				</div>
			</template>
		</el-table-column>
		<el-table-column property="member" label="成员" width="200" show-overflow-tooltip>
			<template #default="scope">
				<div class="member-tags">
					<el-tag
						v-for="(member, index) in splitMembers(scope.row.member)"
						:key="index"
						class="member-tag"
						size="small"
					>
						{{ member }}
					</el-tag>
				</div>
			</template>
		</el-table-column>
		<el-table-column property="createUser" label="创建者" show-overflow-tooltip></el-table-column>
		<el-table-column property="business" label="事业群" show-overflow-tooltip></el-table-column>
		<el-table-column property="manager" label="经理" show-overflow-tooltip></el-table-column>
		<el-table-column property="token" label="Token" width="100" show-overflow-tooltip>
			<template #default="scope">
				<div class="token-display">
					<span class="token-text">{{ scope.row.token ? scope.row.token.slice(0, 8) + '...' : '' }}</span>
					<el-button
						v-if="scope.row.token"
						size="small"
						type="text"
						@click.stop="copyToken(scope.row.token)"
						:icon="CopyDocument"
						title="复制Token"
						class="copy-btn"
					/>
				</div>
			</template>
		</el-table-column>
		<el-table-column property="createAt" label="创建时间" show-overflow-tooltip>
			<template #default="scope">
				{{ formatDate(scope.row.createAt) }}
			</template>
		</el-table-column>
		<el-table-column property="updateAt" label="更新时间" show-overflow-tooltip>
			<template #default="scope">
				{{ formatDate(scope.row.updateAt) }}
			</template>
		</el-table-column>
		<el-table-column label="操作" fixed="right" width="100">
			<template #default="scope">
				<el-button size="small" type="primary" @click="handleEdit(scope.row)" :disabled="!canEdit(scope.row)">
					编辑
				</el-button>
			</template>
		</el-table-column>
	</el-table>
	<!-- 分页组件 -->
	<div class="pagination-container">
		<el-pagination
			v-model:current-page="pagination.currentPage"
			v-model:page-size="pagination.pageSize"
			:total="pagination.total"
			:page-sizes="[10, 20, 50, 100]"
			layout="total, sizes, prev, pager, next, jumper"
			background
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		/>
	</div>

	<GroupFormDialog
		v-model:visible="dialogVisible"
		:formData="currentFormData"
		:isEdit="isEditMode"
		@submit-success="getTableData"
	/>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import GroupApi from '@/api/GroupApi';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores/user';
import GroupFormDialog from './GroupFormDialog.vue';
import type { Group } from '@/form.ts';
import { splitMembers, formatDate } from './utils';
import { CopyDocument } from '@element-plus/icons-vue';

const userStore = useUserStore();

const tableData = ref<Group[]>([]);
const loading = ref(false);
const dialogVisible = ref(false);
const isEditMode = ref(false);
const currentFormData = ref<Group>(getInitialFormData());
const pagination = ref({
	currentPage: 1,
	pageSize: 10,
	total: 0,
});
function getInitialFormData(): Group {
	return {
		id: 0,
		name: '',
		createUser: userStore.username || '',
		business: '',
		manager: '',
		member: '',
		admin: '',
		managerArray: [],
		memberArray: [],
		adminArray: [],
		createAt: '',
		updateAt: '',
	};
}

async function getTableData() {
	loading.value = true;
	try {
		const params = {
			page: pagination.value.currentPage,
			pageSize: pagination.value.pageSize,
		};

		const res = await GroupApi.getGroup(params);
		tableData.value = res.data.items || [];
		pagination.value.total = res.data.total || 0;
	} catch (error) {
		ElMessage.error('获取数据失败');
	} finally {
		loading.value = false;
	}
}

// 分页大小变化
const handleSizeChange = (val: number) => {
	pagination.value.pageSize = val;
	pagination.value.currentPage = 1; // 重置到第一页
	getTableData();
};

// 当前页变化
const handleCurrentChange = (val: number) => {
	pagination.value.currentPage = val;
	getTableData();
};

function canEdit(row: Group): boolean {
	const username = userStore.username;
	return row.admin?.split(';').includes(username);
}

function handleCreate() {
	isEditMode.value = false;
	currentFormData.value = getInitialFormData();
	dialogVisible.value = true;
}

function handleEdit(row: Group) {
	if (!canEdit(row)) {
		ElMessage.warning('您没有权限编辑该群组');
		return;
	}
	isEditMode.value = true;
	currentFormData.value = {
		...row,
		managerArray: row.manager,
		memberArray: row.member?.split(';').filter(Boolean) || [],
		adminArray: row.admin?.split(';').filter(Boolean) || [],
	};
	dialogVisible.value = true;
}

// 在setup中添加copyToken方法
const copyToken = (token: string) => {
	navigator.clipboard
		.writeText(token)
		.then(() => {
			ElMessage.success('Token已复制到剪贴板');
		})
		.catch((err) => {
			console.error('复制失败:', err);
			ElMessage.error('复制失败，请手动复制');
		});
};
getTableData();
</script>

<style scoped>
.token-display {
	display: flex;
	align-items: center;
	gap: 8px;
}

.token-text {
	/* flex: 1; */
	overflow: hidden;
	text-overflow: ellipsis;
}

.copy-btn {
	padding: 0;
	margin-left: 4px;
	color: var(--el-color-primary);
}

/* 分页样式 */
.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: center;
}

.el-pagination {
	padding: 12px 16px;
	background: #fff;
	border-radius: 4px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
</style>
