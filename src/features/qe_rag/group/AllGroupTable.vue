<template>
	<el-button type="primary" @click="handleCreate" style="margin: 16px">新增群组</el-button>
	<el-card>
		<el-form :inline="true" :model="filterForm" class="filter-form">
			<el-form-item label="工作组名称">
				<el-input v-model="filterForm.name" placeholder="输入工作组名称" clearable />
			</el-form-item>
			<el-form-item label="经理">
				<el-input v-model="filterForm.manager" placeholder="输入经理名称" clearable />
			</el-form-item>
			<el-form-item label="事业群名称">
				<el-input v-model="filterForm.business" placeholder="请输入事业群名称" clearable />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handleFilter">查询</el-button>
				<!-- <el-button @click="resetFilter">重置</el-button> -->
			</el-form-item>
		</el-form>
	</el-card>

	<el-table :data="tableData" v-loading="loading" style="width: 100%">
		<el-table-column property="name" label="工作组名称" show-overflow-tooltip />
		<el-table-column property="admin" label="管理员" width="200" show-overflow-tooltip>
			<template #default="scope">
				<div class="member-tags">
					<el-tag
						v-for="(member, index) in splitMembers(scope.row.admin)"
						:key="index"
						class="member-tag"
						size="small"
					>
						{{ member }}
					</el-tag>
				</div>
			</template>
		</el-table-column>
		<el-table-column property="member" label="成员" width="200" show-overflow-tooltip>
			<template #default="scope">
				<div class="member-tags">
					<el-tag
						v-for="(member, index) in splitMembers(scope.row.member)"
						:key="index"
						class="member-tag"
						size="small"
					>
						{{ member }}
					</el-tag>
				</div>
			</template>
		</el-table-column>
		<el-table-column property="createUser" label="创建者" show-overflow-tooltip></el-table-column>
		<el-table-column property="business" label="事业群" show-overflow-tooltip></el-table-column>
		<el-table-column property="manager" label="经理" show-overflow-tooltip></el-table-column>
		<el-table-column property="createAt" label="创建时间" show-overflow-tooltip>
			<template #default="scope">
				{{ formatDate(scope.row.createAt) }}
			</template>
		</el-table-column>
		<el-table-column property="updateAt" label="更新时间" show-overflow-tooltip>
			<template #default="scope">
				{{ formatDate(scope.row.updateAt) }}
			</template>
		</el-table-column>
		<el-table-column label="操作" fixed="right" width="100">
			<template #default="scope">
				<el-button v-if="canEdit(scope.row)" size="small" type="primary" @click="handleEdit(scope.row)">
					编辑
				</el-button>
				<el-button v-else size="small" type="success" @click="handleApply(scope.row)">
					申请
				</el-button>
			</template>
		</el-table-column>
	</el-table>

	<!-- 添加分页组件 -->
	<div class="pagination-container">
		<el-pagination
			v-model:current-page="pagination.currentPage"
			v-model:page-size="pagination.size"
			:total="pagination.total"
			:page-sizes="[10, 20, 50, 100]"
			layout="total, sizes, prev, pager, next, jumper"
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		/>
	</div>
	<GroupFormDialog
		v-model:visible="dialogVisible"
		:formData="currentFormData"
		:isEdit="isEditMode"
		@submit-success="getTableData"
	/>

	<ApplyGroupDialog
		:visible="applyDialogVisible"
		:groupId="currentGroupId"
		:group-name="currentGroupName"
		:admins="currentAdmins"
		@update:visible="applyDialogVisible = $event"
		@submit-success="getTableData"
	/>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import GroupApi from '@/api/GroupApi';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores/user';
import GroupFormDialog from './GroupFormDialog.vue';
import ApplyGroupDialog from './ApplyGroupDialog.vue';
import type { Group } from '@/form.ts';
import { splitMembers, formatDate } from './utils';
const userStore = useUserStore();

// 创建状态状态
const tableData = ref<Group[]>([]);
const loading = ref(false);
const dialogVisible = ref(false);
const isEditMode = ref(false);
const currentFormData = ref<Group>(getInitialFormData());

// 新增申请相关状态
const applyDialogVisible = ref(false);
const currentGroupId = ref(0);
const currentGroupName = ref('');
const currentAdmins = ref<string[]>([]);

// 添加分页状态
const pagination = ref({
	currentPage: 1,
	size: 10,
	total: 0,
});

// 修改筛选表单
const filterForm = ref({
	name: '',
	manager: '',
	business: '',
	page: 1, // 添加页码参数
	size: 10, // 添加每页大小参数
});
// 检查是否是成员
const isMember = (row: Group): boolean => {
	const username = userStore.username;
	if (!username) return false;
	return row.member?.split(';').includes(username) || row.admin?.split(';').includes(username);
};

// 处理申请按钮点击
const handleApply = (row: Group) => {
	currentGroupId.value = row.id;
	currentGroupName.value = row.name;
	currentAdmins.value = splitMembers(row.admin);
	applyDialogVisible.value = true;
};
function getInitialFormData(): Group {
	return {
		id: 0,
		name: '',
		createUser: userStore.username || '',
		business: '',
		manager: '',
		member: '',
		admin: '',
		managerArray: [],
		memberArray: [],
		adminArray: [],
		createAt: '',
		updateAt: '',
	};
}
const filteredTableData = computed(() => {
	return tableData.value.filter((item) => {
		return (
			(filterForm.value.business === '' ||
				(item.business && item.business.includes(filterForm.value.business))) &&
			(filterForm.value.manager === '' || (item.manager && item.manager.includes(filterForm.value.manager))) &&
			(filterForm.value.createUser === '' ||
				(item.createUser && item.createUser.includes(filterForm.value.createUser)))
		);
	});
});

// 筛选方法
const handleFilter = () => {
	// 计算属性会自动更新，这里只需要触发一下
	ElMessage.success('筛选条件已应用');
	console.log(filterForm.value, 178);
	getTableData(filterForm.value);
};

// 分页大小变化
const handleSizeChange = (val: number) => {
	pagination.value.size = val;
	getTableData();
};

// 当前页变化
const handleCurrentChange = (val: number) => {
	pagination.value.currentPage = val;
	getTableData();
};

// 重置筛选时也重置分页
const resetFilter = () => {
	filterForm.value = {
		name: '',
		manager: '',
		business: '',
		page: 1,
		size: pagination.value.size,
	};
	pagination.value.currentPage = 1;
	ElMessage.success('筛选条件已重置');
	getTableData();
};

// 初始化时加载数据
onMounted(() => {
	getTableData();
});
// async function getTableData(data) {
// 	loading.value = true;
// 	try {
// 		const res = await GroupApi.getGroups(data);
// 		tableData.value = res.data;
// 	} catch (error) {
// 		ElMessage.error('获取数据失败');
// 	} finally {
// 		loading.value = false;
// 	}
// }
async function getTableData(params = {}) {
	loading.value = true;
	try {
		// 合并分页参数
		const queryParams = {
			...filterForm.value,
			...params,
			page: pagination.value.currentPage,
			size: pagination.value.size,
		};

		const res = await GroupApi.getGroups(queryParams);
		tableData.value = res.data.items || [];
		pagination.value.total = res.data.total || 0;
	} catch (error) {
		ElMessage.error('获取数据失败');
	} finally {
		loading.value = false;
	}
}
function canEdit(row: Group): boolean {
	const username = userStore.username;
	return row.admin?.split(';').includes(username);
}

function handleCreate() {
	isEditMode.value = false;
	currentFormData.value = getInitialFormData();
	dialogVisible.value = true;
}

function handleEdit(row: Group) {
	if (!canEdit(row)) {
		ElMessage.warning('您没有权限编辑该群组');
		return;
	}
	isEditMode.value = true;
	currentFormData.value = {
		...row,
		managerArray: row.manager,
		memberArray: row.member?.split(';').filter(Boolean) || [],
		adminArray: row.admin?.split(';').filter(Boolean) || [],
	};
	dialogVisible.value = true;
}
</script>

<style scoped>
.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: center; /* 水平居中 */
	align-items: center; /* 垂直居中（如果需要） */
}
</style>
