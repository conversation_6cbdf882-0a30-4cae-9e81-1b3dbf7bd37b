// 群组表单数据类型定义
export const getInitialFormData = () => ({
    id: 0,
    name: '',
    createUser: '',
    business: '',
    manager: '',
    member: '',
    admin: '',
    managerArray: [],
    memberArray: [],
    adminArray: [],
    createAt: '',
    updateAt: '',
});

// 格式化日期
export const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

// 分割成员字符串
export const splitMembers = (memberString) => {
    if (!memberString) return [];
    return memberString.split(';').filter(member => member.trim() !== '');
};
